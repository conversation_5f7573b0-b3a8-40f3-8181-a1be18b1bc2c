// WorkSpace Pro - 工作区专用窗口脚本
// 从URL参数获取工作区信息
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return {
        workspaceId: params.get('workspaceId') || '',
        workspaceName: params.get('workspaceName') || '工作区专用窗口',
        tabCount: parseInt(params.get('tabCount') || '0')
    };
}

// 更新页面内容
function updateContent() {
    const params = getUrlParams();
    
    // 更新工作区名称
    const nameElement = document.getElementById('workspaceName');
    if (nameElement) {
        nameElement.textContent = params.workspaceName;
        document.title = `WorkSpace Pro - ${params.workspaceName}`;
    }

    // 更新标签页数量
    const countElement = document.getElementById('tabCount');
    if (countElement) {
        countElement.textContent = params.tabCount.toString();
    }

    // 根据工作区名称设置图标（简单的映射）
    const iconElement = document.getElementById('workspaceIcon');
    if (iconElement) {
        const name = params.workspaceName.toLowerCase();
        if (name.includes('ai') || name.includes('智能')) {
            iconElement.textContent = '🤖';
        } else if (name.includes('开发') || name.includes('dev')) {
            iconElement.textContent = '💻';
        } else if (name.includes('设计') || name.includes('design')) {
            iconElement.textContent = '🎨';
        } else if (name.includes('研究') || name.includes('research')) {
            iconElement.textContent = '🔬';
        } else if (name.includes('生产力') || name.includes('productivity')) {
            iconElement.textContent = '⚡';
        } else {
            iconElement.textContent = '🚀';
        }
    }
}

// 监听URL变化（用于动态更新）
window.addEventListener('popstate', updateContent);

// 初始化
updateContent();

// 更新标签页计数的函数 - 增强调试版本
async function updateTabCount() {
    console.log('[DEBUG] updateTabCount: 开始更新标签页计数...');
    
    try {
        console.log('[DEBUG] updateTabCount: 检查Chrome API可用性');
        
        if (!chrome) {
            console.error('[DEBUG] updateTabCount: Chrome API 不可用');
            return;
        }
        
        if (!chrome.tabs) {
            console.error('[DEBUG] updateTabCount: Chrome tabs API 不可用');
            return;
        }

        console.log('[DEBUG] updateTabCount: 调用 chrome.tabs.query({ currentWindow: true })');
        const tabs = await chrome.tabs.query({ currentWindow: true });
        console.log('[DEBUG] updateTabCount: chrome.tabs.query 返回结果:', {
            totalTabs: tabs.length,
            tabs: tabs.map(tab => ({ id: tab.id, title: tab.title, url: tab.url }))
        });

        // 更精确地过滤掉占位符页面，但保留其他扩展页面和普通网页
        console.log('[DEBUG] updateTabCount: 开始过滤标签页');
        const workspaceTabs = tabs.filter(tab => {
            const url = tab.url || '';
            const isValid = !url.includes('workspace-placeholder.html');
            
            if (!isValid) {
                console.log('[DEBUG] updateTabCount: 过滤掉占位符标签页:', { id: tab.id, title: tab.title, url });
            }
            
            return isValid;
        });

        console.log('[DEBUG] updateTabCount: 过滤后的工作区标签页:', {
            filteredCount: workspaceTabs.length,
            tabs: workspaceTabs.map(tab => ({ id: tab.id, title: tab.title, url: tab.url }))
        });

        // 更新页面标题以反映标签页数量
        const params = getUrlParams();
        const newTitle = `WorkSpace Pro - ${params.workspaceName} (${workspaceTabs.length} 标签页)`;
        if (document.title !== newTitle) {
            document.title = newTitle;
            console.log('[DEBUG] updateTabCount: 页面标题已更新:', newTitle);
        }

        console.log(`[DEBUG] updateTabCount: 工作区专用窗口 ${params.workspaceName} 当前标签页数量: ${workspaceTabs.length}`);

        // 同时更新标签页列表，确保数据同步
        if (typeof allTabs !== 'undefined' && allTabs.length !== workspaceTabs.length) {
            console.log('[DEBUG] updateTabCount: 标签页数量不同步，重新加载列表');
            await loadTabsList();
        }
        
        console.log('[DEBUG] updateTabCount: 标签页计数更新完成');
    } catch (error) {
        console.error('[DEBUG] updateTabCount: 更新标签页计数时出错:', error);
        console.error('[DEBUG] updateTabCount: 错误详情:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
    }
}

// 立即更新一次
updateTabCount();

// 定期检查标签页数量变化（减少检查频率以提高性能）
setInterval(updateTabCount, 2000);

// 监听标签页变化事件以实现实时更新
if (chrome && chrome.tabs) {
    console.log('[DEBUG] 注册标签页事件监听器');
    
    chrome.tabs.onCreated.addListener(() => {
        console.log('[DEBUG] 标签页创建事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onRemoved.addListener(() => {
        console.log('[DEBUG] 标签页删除事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onMoved.addListener(() => {
        console.log('[DEBUG] 标签页移动事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onAttached.addListener(() => {
        console.log('[DEBUG] 标签页附加事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onDetached.addListener(() => {
        console.log('[DEBUG] 标签页分离事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onUpdated.addListener(() => {
        console.log('[DEBUG] 标签页更新事件触发');
        if (typeof loadTabsList === 'function') loadTabsList();
    });
} else {
    console.warn('[DEBUG] Chrome tabs API 不可用，无法注册事件监听器');
}

// 全局变量
let selectedTabIds = new Set();
let allTabs = [];
let filteredTabs = [];
let searchQuery = '';

// 加载标签页列表 - 增强调试版本
async function loadTabsList() {
    console.log('[DEBUG] loadTabsList: 开始加载标签页列表...');
    
    try {
        console.log('[DEBUG] loadTabsList: 检查Chrome API可用性');
        
        if (!chrome || !chrome.tabs) {
            console.error('[DEBUG] loadTabsList: Chrome tabs API 不可用');
            return;
        }

        console.log('[DEBUG] loadTabsList: 调用 chrome.tabs.query({ currentWindow: true })');
        const tabs = await chrome.tabs.query({ currentWindow: true });
        console.log('[DEBUG] loadTabsList: chrome.tabs.query 返回结果:', {
            totalTabs: tabs.length,
            tabs: tabs.map(tab => ({ id: tab.id, title: tab.title, url: tab.url }))
        });

        console.log('[DEBUG] loadTabsList: 开始过滤标签页');

        // 详细记录每个标签页的信息
        tabs.forEach((tab, index) => {
            console.log(`[DEBUG] loadTabsList: 标签页 ${index + 1}:`, {
                id: tab.id,
                title: tab.title,
                url: tab.url,
                active: tab.active,
                pinned: tab.pinned
            });
        });

        const workspaceTabs = tabs.filter(tab => {
            const url = tab.url || '';
            const isValid = !url.includes('workspace-placeholder.html');

            console.log(`[DEBUG] loadTabsList: 标签页过滤检查:`, {
                id: tab.id,
                title: tab.title,
                url: url,
                isPlaceholder: url.includes('workspace-placeholder.html'),
                isValid: isValid
            });

            if (!isValid) {
                console.log('[DEBUG] loadTabsList: 过滤掉占位符标签页:', { id: tab.id, title: tab.title, url });
            }
            return isValid;
        });

        console.log('[DEBUG] loadTabsList: 过滤后的工作区标签页:', {
            filteredCount: workspaceTabs.length,
            tabs: workspaceTabs.map(tab => ({ id: tab.id, title: tab.title, url: tab.url }))
        });

        // 更新调试信息
        const debugText = document.getElementById('debugText');
        if (debugText) {
            const currentWindow = await chrome.windows.getCurrent();
            debugText.textContent = `窗口ID: ${currentWindow.id}, 原始标签页: ${tabs.length}个, 过滤后: ${workspaceTabs.length}个, 时间: ${new Date().toLocaleTimeString()}`;
        }

        allTabs = workspaceTabs;
        console.log('[DEBUG] loadTabsList: 更新全局 allTabs 变量，长度:', allTabs.length);
        
        applySearchFilter();

        // 始终显示标签页列表区域，即使没有标签页也显示空状态
        const tabsSection = document.getElementById('tabsSection');
        if (tabsSection) {
            tabsSection.style.display = 'block';
            console.log('[DEBUG] loadTabsList: 标签页列表区域已显示');
        } else {
            console.warn('[DEBUG] loadTabsList: 未找到 tabsSection 元素');
        }

        // 更新统计信息
        updateTabsStats();

        console.log('[DEBUG] loadTabsList: 标签页列表加载完成');
    } catch (error) {
        console.error('[DEBUG] loadTabsList: 加载标签页列表时出错:', error);
        console.error('[DEBUG] loadTabsList: 错误详情:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
    }
}

// 应用搜索过滤
function applySearchFilter() {
    console.log('[DEBUG] applySearchFilter: 应用搜索过滤，查询词:', searchQuery);
    
    if (!searchQuery.trim()) {
        filteredTabs = [...allTabs];
        console.log('[DEBUG] applySearchFilter: 无搜索词，显示所有标签页:', filteredTabs.length);
    } else {
        const query = searchQuery.toLowerCase();
        filteredTabs = allTabs.filter(tab => {
            const title = (tab.title || '').toLowerCase();
            const url = (tab.url || '').toLowerCase();
            return title.includes(query) || url.includes(query);
        });
        console.log('[DEBUG] applySearchFilter: 搜索过滤后的标签页数量:', filteredTabs.length);
    }
    
    renderTabsList(filteredTabs);
    updateTabsStats();
}

// 更新标签页统计信息
function updateTabsStats() {
    console.log('[DEBUG] updateTabsStats: 更新统计信息');

    const totalCount = document.getElementById('totalTabsCount');
    const filteredCount = document.getElementById('filteredTabsCount');
    const headerCount = document.getElementById('headerTabsCount');

    if (totalCount) {
        totalCount.textContent = allTabs.length.toString();
        console.log('[DEBUG] updateTabsStats: 总标签页数量已更新:', allTabs.length);
    } else {
        console.warn('[DEBUG] updateTabsStats: 未找到 totalTabsCount 元素');
    }

    if (filteredCount) {
        filteredCount.textContent = filteredTabs.length.toString();
        console.log('[DEBUG] updateTabsStats: 过滤后标签页数量已更新:', filteredTabs.length);
    } else {
        console.warn('[DEBUG] updateTabsStats: 未找到 filteredTabsCount 元素');
    }

    // 更新标题中的计数
    if (headerCount) {
        headerCount.textContent = allTabs.length.toString();
        console.log('[DEBUG] updateTabsStats: 标题中的标签页数量已更新:', allTabs.length);
    } else {
        console.warn('[DEBUG] updateTabsStats: 未找到 headerTabsCount 元素');
    }
}

// 渲染标签页列表
function renderTabsList(tabs) {
    const tabsList = document.getElementById('tabsList');
    if (!tabsList) return;

    if (tabs.length === 0) {
        const emptyMessage = searchQuery.trim() ?
            `<div class="empty-tabs">未找到匹配 "${searchQuery}" 的标签页</div>` :
            '<div class="empty-tabs">暂无存储的标签页</div>';
        tabsList.innerHTML = emptyMessage;
        return;
    }

    tabsList.innerHTML = tabs.map(tab => {
        const title = escapeHtml(tab.title || '无标题');
        const url = escapeHtml(tab.url || '');
        const favicon = tab.favIconUrl || 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg>';

        // 高亮搜索关键词
        const highlightedTitle = highlightSearchTerm(title, searchQuery);
        const highlightedUrl = highlightSearchTerm(url, searchQuery);

        return `
            <div class="tab-item ${selectedTabIds.has(tab.id) ? 'selected' : ''}" data-tab-id="${tab.id}">
                <input type="checkbox" class="tab-checkbox" ${selectedTabIds.has(tab.id) ? 'checked' : ''}
                       onchange="toggleTabSelection(${tab.id})">
                <img class="tab-favicon" src="${favicon}"
                     alt="favicon" onerror="this.style.display='none'">
                <div class="tab-info">
                    <div class="tab-title">${highlightedTitle}</div>
                    <div class="tab-url">${highlightedUrl}</div>
                </div>
                <div class="tab-status">
                    <span class="status-badge status-active">活跃</span>
                    ${tab.pinned ? '<span class="status-badge status-pinned">已固定</span>' : ''}
                </div>
                <div class="tab-actions">
                    <button class="btn-small btn-danger" onclick="deleteTab(${tab.id})" title="删除标签页">删除</button>
                </div>
            </div>
        `;
    }).join('');
}

// 高亮搜索关键词
function highlightSearchTerm(text, query) {
    if (!query.trim()) return text;

    const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
    return text.replace(regex, '<mark class="search-highlight">$1</mark>');
}

// 转义正则表达式特殊字符
function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 切换标签页选择状态
function toggleTabSelection(tabId) {
    if (selectedTabIds.has(tabId)) {
        selectedTabIds.delete(tabId);
    } else {
        selectedTabIds.add(tabId);
    }

    // 更新UI
    const tabItem = document.querySelector(`[data-tab-id="${tabId}"]`);
    if (tabItem) {
        tabItem.classList.toggle('selected', selectedTabIds.has(tabId));
    }

    updateActionButtons();
}

// 更新操作按钮状态
function updateActionButtons() {
    const hasSelection = selectedTabIds.size > 0;
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    if (deleteBtn) deleteBtn.disabled = !hasSelection;

    // 更新全选按钮文本
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (selectAllBtn) {
        if (selectedTabIds.size === filteredTabs.length && filteredTabs.length > 0) {
            selectAllBtn.textContent = '取消全选';
        } else {
            selectAllBtn.textContent = '全选';
        }
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (!selectAllBtn) return;

    if (selectedTabIds.size === filteredTabs.length && filteredTabs.length > 0) {
        // 取消全选
        selectedTabIds.clear();
    } else {
        // 全选当前过滤的标签页
        selectedTabIds.clear();
        filteredTabs.forEach(tab => selectedTabIds.add(tab.id));
    }

    renderTabsList(filteredTabs);
    updateActionButtons();
}

// 搜索功能
function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchQuery = searchInput.value;
        applySearchFilter();
    }
}

// 清除搜索
function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
        searchQuery = '';
        applySearchFilter();
    }
}

// 单个标签页操作

async function deleteTab(tabId) {
    try {
        await chrome.tabs.remove(tabId);
        console.log(`成功删除标签页: ${tabId}`);
        await loadTabsList();
    } catch (error) {
        console.warn(`删除标签页 ${tabId} 失败:`, error);
    }
}



// 显示删除确认对话框
function showDeleteConfirmation() {
    if (selectedTabIds.size === 0) return;

    const modal = document.getElementById('deleteModal');
    const message = document.getElementById('deleteMessage');

    if (modal && message) {
        message.textContent = `确定要删除选中的 ${selectedTabIds.size} 个标签页吗？此操作无法撤销。`;
        modal.style.display = 'flex';
    }
}

// 隐藏删除确认对话框
function hideDeleteConfirmation() {
    const modal = document.getElementById('deleteModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 删除选中的标签页
async function deleteSelectedTabs() {
    if (selectedTabIds.size === 0) return;

    try {
        const tabIds = Array.from(selectedTabIds);
        console.log(`准备删除 ${tabIds.length} 个标签页`);

        await chrome.tabs.remove(tabIds);

        // 清除选择并刷新列表
        selectedTabIds.clear();
        await loadTabsList();
        updateActionButtons();
        hideDeleteConfirmation();

        console.log(`批量删除操作完成`);
    } catch (error) {
        console.error('批量删除标签页时出错:', error);
    }
}

// 初始化事件监听器
function initializeEventListeners() {
    // 搜索输入框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                clearSearch();
            }
        });
    }

    // 清除搜索按钮
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', clearSearch);
    }

    // 刷新按钮
    const refreshTabsBtn = document.getElementById('refreshTabsBtn');
    if (refreshTabsBtn) {
        refreshTabsBtn.addEventListener('click', async () => {
            console.log('[DEBUG] 手动刷新标签页列表');
            await loadTabsList();
        });
    }

    // 创建测试标签页按钮
    const createTestTabBtn = document.getElementById('createTestTabBtn');
    if (createTestTabBtn) {
        createTestTabBtn.addEventListener('click', async () => {
            console.log('[DEBUG] 创建测试标签页');
            try {
                // 在当前窗口创建一个测试标签页
                const tab = await chrome.tabs.create({
                    url: 'https://www.google.com',
                    active: false
                });
                console.log('[DEBUG] 成功创建测试标签页:', tab.id);

                // 等待一下然后刷新列表
                setTimeout(async () => {
                    await loadTabsList();
                }, 1000);
            } catch (error) {
                console.error('[DEBUG] 创建测试标签页失败:', error);
            }
        });
    }

    // 全选按钮
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', toggleSelectAll);
    }



    // 删除选中按钮
    const deleteBtn = document.getElementById('deleteSelectedBtn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', showDeleteConfirmation);
    }

    // 确认删除按钮
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', deleteSelectedTabs);
    }

    // 取消删除按钮
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', hideDeleteConfirmation);
    }

    // 点击模态框背景关闭
    const modal = document.getElementById('deleteModal');
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hideDeleteConfirmation();
            }
        });
    }
}

// 检查Chrome扩展权限和API可用性
async function checkPermissions() {
    console.log('[DEBUG] checkPermissions: 检查Chrome扩展权限...');

    if (!chrome) {
        console.error('[DEBUG] checkPermissions: Chrome API 不可用');
        return false;
    }

    if (!chrome.tabs) {
        console.error('[DEBUG] checkPermissions: Chrome tabs API 不可用');
        return false;
    }

    try {
        // 尝试查询标签页以测试权限
        const tabs = await chrome.tabs.query({ currentWindow: true });
        console.log('[DEBUG] checkPermissions: 权限检查成功，当前窗口标签页数量:', tabs.length);
        return true;
    } catch (error) {
        console.error('[DEBUG] checkPermissions: 权限检查失败:', error);
        return false;
    }
}

// 工作区窗口单例管理
class WorkspaceWindowManager {
    constructor() {
        this.workspaceWindowId = null;
        this.isCreatingWindow = false;
    }

    // 检查窗口是否存在
    async isWindowExists(windowId) {
        if (!windowId) return false;

        try {
            await chrome.windows.get(windowId);
            return true;
        } catch (error) {
            console.log('[DEBUG] WorkspaceWindowManager: 窗口不存在:', windowId);
            return false;
        }
    }

    // 获取或创建工作区窗口
    async getOrCreateWorkspaceWindow() {
        console.log('[DEBUG] WorkspaceWindowManager: 获取或创建工作区窗口');

        // 防止并发创建多个窗口
        if (this.isCreatingWindow) {
            console.log('[DEBUG] WorkspaceWindowManager: 正在创建窗口，等待...');
            // 等待当前创建操作完成
            while (this.isCreatingWindow) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return this.workspaceWindowId;
        }

        // 检查现有窗口是否仍然存在
        if (this.workspaceWindowId && await this.isWindowExists(this.workspaceWindowId)) {
            console.log('[DEBUG] WorkspaceWindowManager: 使用现有工作区窗口:', this.workspaceWindowId);
            return this.workspaceWindowId;
        }

        // 创建新的工作区窗口
        this.isCreatingWindow = true;
        try {
            console.log('[DEBUG] WorkspaceWindowManager: 创建新的工作区窗口');
            const window = await chrome.windows.create({
                url: chrome.runtime.getURL('workspace-placeholder.html'),
                type: 'normal',
                state: 'maximized'
            });

            this.workspaceWindowId = window.id;
            console.log('[DEBUG] WorkspaceWindowManager: 新工作区窗口已创建:', this.workspaceWindowId);

            // 监听窗口关闭事件
            chrome.windows.onRemoved.addListener((windowId) => {
                if (windowId === this.workspaceWindowId) {
                    console.log('[DEBUG] WorkspaceWindowManager: 工作区窗口已关闭，重置ID');
                    this.workspaceWindowId = null;
                }
            });

            return this.workspaceWindowId;
        } catch (error) {
            console.error('[DEBUG] WorkspaceWindowManager: 创建工作区窗口失败:', error);
            this.workspaceWindowId = null;
            throw error;
        } finally {
            this.isCreatingWindow = false;
        }
    }

    // 将标签页移动到工作区窗口
    async moveTabsToWorkspace(tabIds) {
        console.log('[DEBUG] WorkspaceWindowManager: 移动标签页到工作区:', tabIds);

        try {
            const windowId = await this.getOrCreateWorkspaceWindow();
            if (!windowId) {
                throw new Error('无法获取工作区窗口');
            }

            // 移动标签页到工作区窗口
            await chrome.tabs.move(tabIds, { windowId, index: -1 });
            console.log('[DEBUG] WorkspaceWindowManager: 标签页移动完成');

            // 激活工作区窗口
            await chrome.windows.update(windowId, { focused: true });

            return windowId;
        } catch (error) {
            console.error('[DEBUG] WorkspaceWindowManager: 移动标签页失败:', error);
            throw error;
        }
    }
}

// 创建全局工作区窗口管理器实例
const workspaceWindowManager = new WorkspaceWindowManager();

// 初始化所有功能
async function initializeAll() {
    console.log('[DEBUG] initializeAll: 开始初始化页面...');
    console.log('[DEBUG] initializeAll: 当前URL:', window.location.href);
    console.log('[DEBUG] initializeAll: Document readyState:', document.readyState);

    // 调试URL参数
    const params = getUrlParams();
    console.log('[DEBUG] initializeAll: URL参数:', params);

    // 检查权限
    const hasPermissions = await checkPermissions();
    if (!hasPermissions) {
        console.error('[DEBUG] initializeAll: 缺少必要权限，无法正常工作');
        // 显示错误信息给用户
        const tabsSection = document.getElementById('tabsSection');
        if (tabsSection) {
            tabsSection.innerHTML = `
                <div class="error-message">
                    <h3>⚠️ 权限错误</h3>
                    <p>扩展缺少必要的标签页访问权限。请检查扩展设置。</p>
                </div>
            `;
            tabsSection.style.display = 'block';
        }
        return;
    }

    initializeEventListeners();
    await updateTabCount();
    await loadTabsList();

    // 设置定期更新
    setInterval(async () => {
        await updateTabCount();
        await loadTabsList();
    }, 3000); // 每3秒更新一次

    console.log('[DEBUG] initializeAll: 页面初始化完成');
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAll);
} else {
    // DOM已经加载完成，立即初始化
    initializeAll();
}
