<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorkSpace Pro - 工作区专用窗口</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #e2e8f0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            width: 100%;
            text-align: center;
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(148, 163, 184, 0.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .workspace-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            display: inline-block;
            padding: 1rem;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }

        .workspace-name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .workspace-description {
            font-size: 1.2rem;
            color: #94a3b8;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: rgba(51, 65, 85, 0.6);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #60a5fa;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .info-section {
            background: rgba(51, 65, 85, 0.4);
            border-radius: 12px;
            padding: 2rem;
            margin-top: 2rem;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .info-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #f1f5f9;
        }

        .info-list {
            list-style: none;
            text-align: left;
        }

        .info-list li {
            padding: 0.5rem 0;
            color: #cbd5e1;
            display: flex;
            align-items: center;
        }

        .info-list li::before {
            content: "✨";
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .footer {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(148, 163, 184, 0.1);
            color: #64748b;
            font-size: 0.9rem;
        }

        .brand {
            font-weight: 600;
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .container {
            animation: fadeIn 0.6s ease-out;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        /* 标签页列表样式 */
        .tabs-section {
            background: rgba(51, 65, 85, 0.4);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid rgba(148, 163, 184, 0.1);
            max-height: 400px;
            overflow-y: auto;
        }

        .tabs-header {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(148, 163, 184, 0.1);
        }

        .tabs-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #f1f5f9;
        }

        .tabs-search {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 0.5rem 0.75rem;
            background: rgba(51, 65, 85, 0.6);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 6px;
            color: #f1f5f9;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }

        .search-input::placeholder {
            color: #94a3b8;
        }

        .tabs-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .tabs-stats {
            margin-bottom: 1rem;
            padding: 0.5rem 0.75rem;
            background: rgba(51, 65, 85, 0.3);
            border-radius: 6px;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .stats-text {
            font-size: 0.85rem;
            color: #94a3b8;
        }

        .empty-tabs {
            text-align: center;
            padding: 2rem;
            color: #94a3b8;
            font-size: 0.9rem;
            background: rgba(30, 41, 59, 0.3);
            border-radius: 8px;
            border: 1px dashed rgba(148, 163, 184, 0.2);
        }

        .error-message {
            text-align: center;
            padding: 2rem;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .error-message h3 {
            color: #ef4444;
            margin-bottom: 1rem;
        }

        .error-message p {
            color: #fca5a5;
            margin: 0;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .btn-secondary {
            background: rgba(71, 85, 105, 0.8);
            color: #e2e8f0;
        }

        .btn-secondary:hover {
            background: rgba(71, 85, 105, 1);
        }

        .btn-success {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
        }

        .tab-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(30, 41, 59, 0.6);
            border-radius: 8px;
            border: 1px solid rgba(148, 163, 184, 0.1);
            transition: all 0.2s;
        }

        .tab-item:hover {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(148, 163, 184, 0.2);
        }

        .tab-item.selected {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.4);
        }

        .tab-checkbox {
            margin-right: 0.75rem;
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .search-highlight {
            background: rgba(251, 191, 36, 0.3);
            color: #fbbf24;
            padding: 0.1rem 0.2rem;
            border-radius: 3px;
        }

        .tab-actions {
            display: flex;
            gap: 0.25rem;
            margin-left: 0.5rem;
        }

        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .btn-small:hover {
            transform: translateY(-1px);
        }

        .btn-small:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .tab-favicon {
            width: 16px;
            height: 16px;
            margin-right: 0.5rem;
            border-radius: 2px;
        }

        .tab-info {
            flex: 1;
            min-width: 0;
        }

        .tab-title {
            font-size: 0.9rem;
            color: #f1f5f9;
            margin-bottom: 0.2rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tab-url {
            font-size: 0.75rem;
            color: #94a3b8;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tab-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: 0.5rem;
        }

        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .status-suspended {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
        }

        .status-active {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .status-pinned {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }

        .empty-tabs {
            text-align: center;
            padding: 2rem;
            color: #94a3b8;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: rgba(30, 41, 59, 0.95);
            border-radius: 12px;
            padding: 2rem;
            max-width: 400px;
            width: 90%;
            border: 1px solid rgba(148, 163, 184, 0.2);
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 1rem;
        }

        .modal-message {
            color: #cbd5e1;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .modal-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="workspace-icon" id="workspaceIcon">
            🚀
        </div>
        
        <h1 class="workspace-name" id="workspaceName">
            工作区专用窗口
        </h1>
        
        <p class="workspace-description">
            此窗口专门用于存储和管理工作区的标签页。<br>
            当您切换到其他工作区时，相关标签页会自动移动到这里保存。
        </p>





        <!-- 标签页列表区域 -->
        <div class="tabs-section" id="tabsSection">
            <div class="tabs-header">
                <h3 class="tabs-title">📋 存储的标签页 (<span id="headerTabsCount">0</span>个)</h3>
                <div class="tabs-search">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索标签页标题或URL...">
                    <button class="btn btn-secondary" id="clearSearchBtn">清除</button>
                </div>
                <div class="tabs-actions">
                    <button class="btn btn-secondary" id="selectAllBtn">全选</button>
                    <button class="btn btn-danger" id="deleteSelectedBtn" disabled>删除选中</button>
                </div>
            </div>
            <div class="tabs-stats" id="tabsStats">
                <span class="stats-text">共 <span id="totalTabsCount">0</span> 个标签页，显示 <span id="filteredTabsCount">0</span> 个</span>
            </div>
            <div id="tabsList" class="tabs-list">
                <!-- 标签页列表将在这里动态生成 -->
            </div>
        </div>

        <!-- 确认删除模态框 -->
        <div class="modal" id="deleteModal" style="display: none;">
            <div class="modal-content">
                <h3 class="modal-title">⚠️ 确认删除</h3>
                <p class="modal-message" id="deleteMessage">
                    确定要删除选中的标签页吗？此操作无法撤销。
                </p>
                <div class="modal-actions">
                    <button class="btn btn-secondary" id="cancelDeleteBtn">取消</button>
                    <button class="btn btn-danger" id="confirmDeleteBtn">删除</button>
                </div>
            </div>
        </div>

        <div class="footer">
            由 <span class="brand">WorkSpace Pro</span> 提供 • 专业的标签页工作区管理
        </div>
    </div>

    <script>
        // 从URL参数获取工作区信息
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                workspaceId: params.get('workspaceId') || '',
                workspaceName: params.get('workspaceName') || '工作区专用窗口',
                tabCount: parseInt(params.get('tabCount') || '0')
            };
        }

        // 更新页面内容
        function updateContent() {
            const params = getUrlParams();
            
            // 更新工作区名称
            const nameElement = document.getElementById('workspaceName');
            if (nameElement) {
                nameElement.textContent = params.workspaceName;
                document.title = `WorkSpace Pro - ${params.workspaceName}`;
            }

            // 更新标签页数量
            const countElement = document.getElementById('tabCount');
            if (countElement) {
                countElement.textContent = params.tabCount.toString();
            }

            // 根据工作区名称设置图标（简单的映射）
            const iconElement = document.getElementById('workspaceIcon');
            if (iconElement) {
                const name = params.workspaceName.toLowerCase();
                if (name.includes('ai') || name.includes('智能')) {
                    iconElement.textContent = '🤖';
                } else if (name.includes('开发') || name.includes('dev')) {
                    iconElement.textContent = '💻';
                } else if (name.includes('设计') || name.includes('design')) {
                    iconElement.textContent = '🎨';
                } else if (name.includes('研究') || name.includes('research')) {
                    iconElement.textContent = '🔬';
                } else if (name.includes('生产力') || name.includes('productivity')) {
                    iconElement.textContent = '⚡';
                } else {
                    iconElement.textContent = '🚀';
                }
            }
        }

        // 监听URL变化（用于动态更新）
        window.addEventListener('popstate', updateContent);
        
        // 初始化
        updateContent();

        // 更新标签页计数的函数
        async function updateTabCount() {
            try {
                console.log('开始更新标签页计数...');

                if (chrome && chrome.tabs) {
                    const tabs = await chrome.tabs.query({ currentWindow: true });
                    console.log('updateTabCount - 当前窗口所有标签页:', tabs.length);

                    // 更精确地过滤掉占位符页面，但保留其他扩展页面和普通网页
                    const workspaceTabs = tabs.filter(tab => {
                        const url = tab.url || '';
                        // 只过滤掉当前的占位符页面，保留所有其他标签页
                        return !url.includes('workspace-placeholder.html');
                    });

                    console.log('updateTabCount - 过滤后的工作区标签页:', workspaceTabs.length);

                    // 更新页面标题以反映标签页数量
                    const params = getUrlParams();
                    const newTitle = `WorkSpace Pro - ${params.workspaceName} (${workspaceTabs.length} 标签页)`;
                    if (document.title !== newTitle) {
                        document.title = newTitle;
                        console.log('页面标题已更新:', newTitle);
                    }

                    console.log(`工作区专用窗口 ${params.workspaceName} 当前标签页数量: ${workspaceTabs.length}`);

                    // 同时更新标签页列表，确保数据同步
                    if (allTabs.length !== workspaceTabs.length) {
                        console.log('标签页数量不同步，重新加载列表');
                        await loadTabsList();
                    }
                } else {
                    console.warn('Chrome tabs API 不可用');
                }
            } catch (error) {
                console.error('更新标签页计数时出错:', error);
            }
        }

        // 立即更新一次
        updateTabCount();

        // 定期检查标签页数量变化（减少检查频率以提高性能）
        setInterval(updateTabCount, 2000);

        // 监听标签页变化事件以实现实时更新
        if (chrome && chrome.tabs) {
            chrome.tabs.onCreated.addListener(() => {
                updateTabCount();
                loadTabsList();
            });
            chrome.tabs.onRemoved.addListener(() => {
                updateTabCount();
                loadTabsList();
            });
            chrome.tabs.onMoved.addListener(() => {
                updateTabCount();
                loadTabsList();
            });
            chrome.tabs.onAttached.addListener(() => {
                updateTabCount();
                loadTabsList();
            });
            chrome.tabs.onDetached.addListener(() => {
                updateTabCount();
                loadTabsList();
            });
            chrome.tabs.onUpdated.addListener(() => {
                loadTabsList();
            });
        }

        // 全局变量
        let selectedTabIds = new Set();
        let allTabs = [];
        let filteredTabs = [];
        let searchQuery = '';

        // 加载标签页列表
        async function loadTabsList() {
            try {
                console.log('开始加载标签页列表...');

                if (!chrome || !chrome.tabs) {
                    console.log('Chrome tabs API 不可用');
                    return;
                }

                const tabs = await chrome.tabs.query({ currentWindow: true });
                console.log('当前窗口所有标签页:', tabs.length, tabs);

                const workspaceTabs = tabs.filter(tab => {
                    const url = tab.url || '';
                    // 只过滤掉当前的占位符页面，保留所有其他标签页
                    const isValid = !url.includes('workspace-placeholder.html');

                    if (!isValid) {
                        console.log('过滤掉标签页:', tab.title, url);
                    }
                    return isValid;
                });

                console.log('过滤后的工作区标签页:', workspaceTabs.length, workspaceTabs);

                allTabs = workspaceTabs;
                applySearchFilter();

                // 始终显示标签页列表区域，即使没有标签页也显示空状态
                const tabsSection = document.getElementById('tabsSection');
                if (tabsSection) {
                    tabsSection.style.display = 'block';
                    console.log('标签页列表区域已显示');
                }

                // 更新统计信息
                updateTabsStats();

                console.log('标签页列表加载完成');
            } catch (error) {
                console.error('加载标签页列表时出错:', error);
            }
        }

        // 应用搜索过滤
        function applySearchFilter() {
            if (!searchQuery.trim()) {
                filteredTabs = [...allTabs];
            } else {
                const query = searchQuery.toLowerCase();
                filteredTabs = allTabs.filter(tab => {
                    const title = (tab.title || '').toLowerCase();
                    const url = (tab.url || '').toLowerCase();
                    return title.includes(query) || url.includes(query);
                });
            }
            renderTabsList(filteredTabs);
            updateTabsStats();
        }

        // 更新标签页统计信息
        function updateTabsStats() {
            const totalCount = document.getElementById('totalTabsCount');
            const filteredCount = document.getElementById('filteredTabsCount');

            if (totalCount) totalCount.textContent = allTabs.length.toString();
            if (filteredCount) filteredCount.textContent = filteredTabs.length.toString();
        }

        // 渲染标签页列表
        function renderTabsList(tabs) {
            const tabsList = document.getElementById('tabsList');
            if (!tabsList) return;

            if (tabs.length === 0) {
                const emptyMessage = searchQuery.trim() ?
                    `<div class="empty-tabs">未找到匹配 "${searchQuery}" 的标签页</div>` :
                    '<div class="empty-tabs">暂无存储的标签页</div>';
                tabsList.innerHTML = emptyMessage;
                return;
            }

            tabsList.innerHTML = tabs.map(tab => {
                const title = escapeHtml(tab.title || '无标题');
                const url = escapeHtml(tab.url || '');
                const favicon = tab.favIconUrl || 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg>';

                // 高亮搜索关键词
                const highlightedTitle = highlightSearchTerm(title, searchQuery);
                const highlightedUrl = highlightSearchTerm(url, searchQuery);

                return `
                    <div class="tab-item ${selectedTabIds.has(tab.id) ? 'selected' : ''}" data-tab-id="${tab.id}">
                        <input type="checkbox" class="tab-checkbox" ${selectedTabIds.has(tab.id) ? 'checked' : ''}
                               onchange="toggleTabSelection(${tab.id})">
                        <img class="tab-favicon" src="${favicon}"
                             alt="favicon" onerror="this.style.display='none'">
                        <div class="tab-info">
                            <div class="tab-title">${highlightedTitle}</div>
                            <div class="tab-url">${highlightedUrl}</div>
                        </div>
                        <div class="tab-status">
                            ${tab.discarded ? '<span class="status-badge status-suspended">已挂起</span>' : '<span class="status-badge status-active">活跃</span>'}
                            ${tab.pinned ? '<span class="status-badge status-pinned">已固定</span>' : ''}
                        </div>
                        <div class="tab-actions">
                            ${tab.discarded ?
                                `<button class="btn-small btn-success" onclick="restoreTab(${tab.id})" title="恢复标签页">恢复</button>` :
                                `<button class="btn-small btn-primary" onclick="suspendTab(${tab.id})" title="挂起标签页">挂起</button>`
                            }
                            <button class="btn-small btn-danger" onclick="deleteTab(${tab.id})" title="删除标签页">删除</button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 高亮搜索关键词
        function highlightSearchTerm(text, query) {
            if (!query.trim()) return text;

            const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
            return text.replace(regex, '<mark class="search-highlight">$1</mark>');
        }

        // 转义正则表达式特殊字符
        function escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 切换标签页选择状态
        function toggleTabSelection(tabId) {
            if (selectedTabIds.has(tabId)) {
                selectedTabIds.delete(tabId);
            } else {
                selectedTabIds.add(tabId);
            }

            // 更新UI
            const tabItem = document.querySelector(`[data-tab-id="${tabId}"]`);
            if (tabItem) {
                tabItem.classList.toggle('selected', selectedTabIds.has(tabId));
            }

            updateActionButtons();
        }

        // 更新操作按钮状态
        function updateActionButtons() {
            const hasSelection = selectedTabIds.size > 0;
            const suspendBtn = document.getElementById('suspendSelectedBtn');
            const restoreBtn = document.getElementById('restoreSelectedBtn');
            const deleteBtn = document.getElementById('deleteSelectedBtn');

            if (suspendBtn) suspendBtn.disabled = !hasSelection;
            if (restoreBtn) restoreBtn.disabled = !hasSelection;
            if (deleteBtn) deleteBtn.disabled = !hasSelection;

            // 更新全选按钮文本
            const selectAllBtn = document.getElementById('selectAllBtn');
            if (selectAllBtn) {
                if (selectedTabIds.size === filteredTabs.length && filteredTabs.length > 0) {
                    selectAllBtn.textContent = '取消全选';
                } else {
                    selectAllBtn.textContent = '全选';
                }
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAllBtn = document.getElementById('selectAllBtn');
            if (!selectAllBtn) return;

            if (selectedTabIds.size === filteredTabs.length && filteredTabs.length > 0) {
                // 取消全选
                selectedTabIds.clear();
            } else {
                // 全选当前过滤的标签页
                selectedTabIds.clear();
                filteredTabs.forEach(tab => selectedTabIds.add(tab.id));
            }

            renderTabsList(filteredTabs);
            updateActionButtons();
        }

        // 搜索功能
        function handleSearch() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchQuery = searchInput.value;
                applySearchFilter();
            }
        }

        // 清除搜索
        function clearSearch() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
                searchQuery = '';
                applySearchFilter();
            }
        }

        // 单个标签页操作
        async function suspendTab(tabId) {
            try {
                await chrome.tabs.discard(tabId);
                console.log(`成功挂起标签页: ${tabId}`);
                await loadTabsList();
            } catch (error) {
                console.warn(`挂起标签页 ${tabId} 失败:`, error);
            }
        }

        async function restoreTab(tabId) {
            try {
                await chrome.tabs.reload(tabId);
                console.log(`成功恢复标签页: ${tabId}`);
                await loadTabsList();
            } catch (error) {
                console.warn(`恢复标签页 ${tabId} 失败:`, error);
            }
        }

        async function deleteTab(tabId) {
            try {
                await chrome.tabs.remove(tabId);
                console.log(`成功删除标签页: ${tabId}`);
                await loadTabsList();
            } catch (error) {
                console.warn(`删除标签页 ${tabId} 失败:`, error);
            }
        }

        // 挂起选中的标签页
        async function suspendSelectedTabs() {
            if (selectedTabIds.size === 0) return;

            try {
                const tabIds = Array.from(selectedTabIds);
                console.log(`准备挂起 ${tabIds.length} 个标签页`);

                for (const tabId of tabIds) {
                    try {
                        await chrome.tabs.discard(tabId);
                        console.log(`成功挂起标签页: ${tabId}`);
                    } catch (error) {
                        console.warn(`挂起标签页 ${tabId} 失败:`, error);
                    }
                }

                // 清除选择并刷新列表
                selectedTabIds.clear();
                await loadTabsList();
                updateActionButtons();

                console.log(`批量挂起操作完成`);
            } catch (error) {
                console.error('批量挂起标签页时出错:', error);
            }
        }

        // 恢复选中的标签页
        async function restoreSelectedTabs() {
            if (selectedTabIds.size === 0) return;

            try {
                const tabIds = Array.from(selectedTabIds);
                console.log(`准备恢复 ${tabIds.length} 个标签页`);

                for (const tabId of tabIds) {
                    try {
                        await chrome.tabs.reload(tabId);
                        console.log(`成功恢复标签页: ${tabId}`);
                    } catch (error) {
                        console.warn(`恢复标签页 ${tabId} 失败:`, error);
                    }
                }

                // 清除选择并刷新列表
                selectedTabIds.clear();
                await loadTabsList();
                updateActionButtons();

                console.log(`批量恢复操作完成`);
            } catch (error) {
                console.error('批量恢复标签页时出错:', error);
            }
        }

        // 显示删除确认对话框
        function showDeleteConfirmation() {
            if (selectedTabIds.size === 0) return;

            const modal = document.getElementById('deleteModal');
            const message = document.getElementById('deleteMessage');

            if (modal && message) {
                message.textContent = `确定要删除选中的 ${selectedTabIds.size} 个标签页吗？此操作无法撤销。`;
                modal.style.display = 'flex';
            }
        }

        // 隐藏删除确认对话框
        function hideDeleteConfirmation() {
            const modal = document.getElementById('deleteModal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        // 删除选中的标签页
        async function deleteSelectedTabs() {
            if (selectedTabIds.size === 0) return;

            try {
                const tabIds = Array.from(selectedTabIds);
                console.log(`准备删除 ${tabIds.length} 个标签页`);

                await chrome.tabs.remove(tabIds);

                // 清除选择并刷新列表
                selectedTabIds.clear();
                await loadTabsList();
                updateActionButtons();
                hideDeleteConfirmation();

                console.log(`批量删除操作完成`);
            } catch (error) {
                console.error('批量删除标签页时出错:', error);
            }
        }

        // 初始化事件监听器
        function initializeEventListeners() {
            // 搜索输入框
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', handleSearch);
                searchInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        clearSearch();
                    }
                });
            }

            // 清除搜索按钮
            const clearSearchBtn = document.getElementById('clearSearchBtn');
            if (clearSearchBtn) {
                clearSearchBtn.addEventListener('click', clearSearch);
            }

            // 全选按钮
            const selectAllBtn = document.getElementById('selectAllBtn');
            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', toggleSelectAll);
            }

            // 挂起选中按钮
            const suspendBtn = document.getElementById('suspendSelectedBtn');
            if (suspendBtn) {
                suspendBtn.addEventListener('click', suspendSelectedTabs);
            }

            // 恢复选中按钮
            const restoreBtn = document.getElementById('restoreSelectedBtn');
            if (restoreBtn) {
                restoreBtn.addEventListener('click', restoreSelectedTabs);
            }

            // 删除选中按钮
            const deleteBtn = document.getElementById('deleteSelectedBtn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', showDeleteConfirmation);
            }

            // 确认删除按钮
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
            if (confirmDeleteBtn) {
                confirmDeleteBtn.addEventListener('click', deleteSelectedTabs);
            }

            // 取消删除按钮
            const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
            if (cancelDeleteBtn) {
                cancelDeleteBtn.addEventListener('click', hideDeleteConfirmation);
            }

            // 点击模态框背景关闭
            const modal = document.getElementById('deleteModal');
            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        hideDeleteConfirmation();
                    }
                });
            }
        }

        // 检查Chrome扩展权限和API可用性
        async function checkPermissions() {
            console.log('检查Chrome扩展权限...');

            if (!chrome) {
                console.error('Chrome API 不可用');
                return false;
            }

            if (!chrome.tabs) {
                console.error('Chrome tabs API 不可用');
                return false;
            }

            try {
                // 尝试查询标签页以测试权限
                const tabs = await chrome.tabs.query({ currentWindow: true });
                console.log('权限检查成功，当前窗口标签页数量:', tabs.length);
                return true;
            } catch (error) {
                console.error('权限检查失败:', error);
                return false;
            }
        }

        // 初始化所有功能
        async function initializeAll() {
            console.log('开始初始化页面...');
            console.log('当前URL:', window.location.href);
            console.log('Document readyState:', document.readyState);

            // 检查权限
            const hasPermissions = await checkPermissions();
            if (!hasPermissions) {
                console.error('缺少必要权限，无法正常工作');
                // 显示错误信息给用户
                const tabsSection = document.getElementById('tabsSection');
                if (tabsSection) {
                    tabsSection.innerHTML = `
                        <div class="error-message">
                            <h3>⚠️ 权限错误</h3>
                            <p>扩展缺少必要的标签页访问权限。请检查扩展设置。</p>
                        </div>
                    `;
                    tabsSection.style.display = 'block';
                }
                return;
            }

            initializeEventListeners();
            await updateTabCount();
            await loadTabsList();

            // 设置定期更新
            setInterval(async () => {
                await updateTabCount();
                await loadTabsList();
            }, 3000); // 每3秒更新一次

            console.log('页面初始化完成');
        }

        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeAll);
        } else {
            // DOM已经加载完成，立即初始化
            initializeAll();
        }
    </script>
</body>
</html>
