# 专用窗口架构测试指南

## 🎯 测试目标

验证 WorkSpace Pro 的专用窗口架构是否正确实现了 Workona 风格的标签页管理：

- ✅ 主工作窗口与专用存储窗口的分离
- ✅ 标签页在窗口间的无缝移动
- ✅ 完整保持标签页状态（DevTools、表单、滚动位置等）
- ✅ 工作区切换的瞬间响应
- ✅ 专用窗口的自动管理

## 🏗️ 架构概览

```
主工作窗口（用户看到的）
    ↕️ 标签页移动
工作区一专用窗口（后台存储）
工作区二专用窗口（后台存储）
工作区三专用窗口（后台存储）
...
```

## 📋 测试步骤

### 1. 基础环境准备

1. **加载扩展**
   - 在 Chrome 中加载 `dist` 目录作为未打包扩展
   - 确认扩展图标出现在工具栏中
   - 点击扩展图标打开侧边栏

2. **创建测试工作区**
   ```
   工作区一：AI工具 (🤖)
   - https://chat.openai.com
   - https://claude.ai
   
   工作区二：开发环境 (💻)
   - https://github.com
   - https://stackoverflow.com
   
   工作区三：设计工具 (🎨)
   - https://www.figma.com
   - https://dribbble.com
   ```

### 2. 专用窗口创建测试

**测试场景：** 验证专用窗口是否正确创建

1. 创建第一个工作区 "AI工具"
2. 添加网站 https://chat.openai.com
3. 切换到该工作区

**预期结果：**
- ✅ 自动创建专用窗口（窗口标题包含工作区名称）
- ✅ 专用窗口显示工作区说明页面
- ✅ 主窗口打开 ChatGPT 标签页
- ✅ 专用窗口在任务栏可见但不获取焦点

**验证方法：**
```javascript
// 在控制台执行
chrome.windows.getAll({populate: true}).then(windows => {
  console.log('所有窗口:', windows.map(w => ({
    id: w.id,
    type: w.type,
    state: w.state,
    tabs: w.tabs.map(t => t.title)
  })));
});
```

### 3. 标签页移动测试

**测试场景：** 验证标签页在窗口间的移动

1. 在主窗口打开多个标签页：
   - ChatGPT
   - Claude
   - 一个随机网站（如 Google）

2. 创建第二个工作区 "开发环境"
3. 切换到 "开发环境" 工作区

**预期结果：**
- ✅ ChatGPT 和 Claude 标签页移动到 "AI工具" 专用窗口
- ✅ 随机网站标签页保留在主窗口
- ✅ "开发环境" 的标签页（如果有）移动到主窗口
- ✅ 所有标签页状态完全保持

**验证方法：**
1. 在 ChatGPT 中输入一些文字但不发送
2. 在 Claude 中打开 DevTools
3. 切换工作区后检查这些状态是否保持

### 4. 状态保持测试

**测试场景：** 验证标签页状态的完整保持

1. 在 ChatGPT 中：
   - 输入一段文字但不发送
   - 滚动到页面中间位置
   - 打开 DevTools

2. 在 GitHub 中：
   - 登录账户
   - 打开一个仓库页面
   - 在搜索框中输入内容

3. 切换工作区多次

**预期结果：**
- ✅ 文本输入内容保持
- ✅ 滚动位置保持
- ✅ DevTools 状态保持
- ✅ 登录状态保持
- ✅ 表单内容保持

### 5. 工作区删除测试

**测试场景：** 验证工作区删除时专用窗口的清理

1. 创建一个测试工作区
2. 切换到该工作区，确保有标签页在专用窗口中
3. 删除该工作区

**预期结果：**
- ✅ 专用窗口中的标签页移动回主窗口
- ✅ 专用窗口自动关闭
- ✅ 没有孤立的窗口残留

### 6. 性能测试

**测试场景：** 验证切换性能和响应速度

1. 创建 5 个工作区，每个包含 3-5 个标签页
2. 快速连续切换工作区
3. 测量切换响应时间

**预期结果：**
- ✅ 切换响应时间 < 500ms
- ✅ 没有明显的延迟或卡顿
- ✅ 标签页移动流畅
- ✅ UI 响应及时

## 🔍 调试工具

### 1. 控制台命令

```javascript
// 查看所有窗口
chrome.windows.getAll({populate: true}).then(console.log);

// 查看 WindowManager 状态
// (需要在扩展的 background script 中执行)
console.log('工作区窗口映射:', WindowManager.workspaceWindows);
console.log('窗口工作区映射:', WindowManager.windowWorkspaces);

// 查看当前标签页
chrome.tabs.query({}).then(tabs => {
  console.log('所有标签页:', tabs.map(t => ({
    id: t.id,
    windowId: t.windowId,
    title: t.title,
    url: t.url
  })));
});
```

### 2. 专用窗口说明页面

专用窗口会显示实时的标签页数量和工作区信息，可以通过以下方式监控：

- 窗口标题显示工作区名称和标签页数量
- 页面内容显示详细的工作区信息
- 控制台输出标签页变化日志

## ⚠️ 常见问题排查

### 问题1：专用窗口没有创建
**可能原因：**
- 权限不足（检查 manifest.json 中的 "windows" 权限）
- WindowManager 初始化失败

**排查方法：**
```javascript
// 检查权限
chrome.permissions.contains({permissions: ['windows']}).then(console.log);
```

### 问题2：标签页移动失败
**可能原因：**
- 标签页被固定或有特殊限制
- 窗口ID无效

**排查方法：**
```javascript
// 检查标签页状态
chrome.tabs.query({}).then(tabs => {
  tabs.forEach(tab => {
    console.log(`标签页 ${tab.id}: pinned=${tab.pinned}, url=${tab.url}`);
  });
});
```

### 问题3：状态丢失
**可能原因：**
- 标签页被重新加载而非移动
- Chrome 内部限制

**排查方法：**
- 检查控制台是否有错误信息
- 验证 chrome.tabs.move() 调用是否成功

## 🎉 成功标准

专用窗口架构测试通过的标准：

1. ✅ 所有专用窗口正确创建和管理
2. ✅ 标签页移动无状态丢失
3. ✅ 工作区切换响应迅速
4. ✅ 窗口生命周期管理正确
5. ✅ 用户体验流畅自然
6. ✅ 没有内存泄漏或孤立窗口

通过这些测试，我们可以确保 WorkSpace Pro 的专用窗口架构达到了 Workona 的标准，为用户提供了完美的多工作区标签页管理体验。
