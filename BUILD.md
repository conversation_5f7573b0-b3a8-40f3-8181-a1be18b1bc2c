# 🔧 WorkSpace Pro - 构建指南

## 📋 构建脚本说明

### 可用命令

```bash
# 完整构建（推荐）- 包含验证
npm run build

# 仅构建（不验证）
npm run build-only

# 带类型检查的构建
npm run build-with-types

# 仅验证现有构建
npm run verify
```

## 🎯 构建过程

### 1. **Vite构建**
- 编译TypeScript和React代码
- 生成优化的JavaScript和CSS文件
- 创建sidepanel.html和background.js

### 2. **自定义文件复制**
- 复制`public/manifest.json`到`dist/manifest.json`
- 复制`public/workspace-placeholder.html`到`dist/workspace-placeholder.html`
- 复制`public/workspace-placeholder.js`到`dist/workspace-placeholder.js`
- 复制所有图标文件到`dist/icons/`

### 3. **构建验证**
- 检查所有必需文件是否存在
- 验证文件大小和内容
- 确认workspace-placeholder.js包含删除功能

## 📁 构建输出结构

```
dist/
├── manifest.json           # Chrome扩展清单
├── background.js           # 后台脚本
├── sidepanel.html          # 侧边栏页面
├── workspace-placeholder.html  # 工作区占位符页面
├── workspace-placeholder.js   # 工作区占位符脚本
├── icons/                  # 扩展图标
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── assets/                 # 编译后的资源
    ├── sidepanel-[hash].js
    ├── sidepanel-[hash].css
    └── workspaceSwitcher-[hash].js
```

## ⚠️ 重要说明

### workspace-placeholder文件处理
- 这些文件**不会**被Vite处理或转换
- 它们会被**原样复制**到dist根目录
- 确保在Chrome扩展中可以直接访问

### 构建验证
- 每次构建后自动运行验证
- 如果验证失败，构建过程会报错
- 可以单独运行`npm run verify`检查现有构建

## 🔍 故障排除

### 常见问题

1. **文件缺失错误**
   ```bash
   npm run verify  # 检查哪些文件缺失
   ```

2. **权限问题**
   ```bash
   chmod +x scripts/verify-build.js
   ```

3. **清理重建**
   ```bash
   rm -rf dist && npm run build
   ```

### 调试模式
构建配置中`minify: false`便于调试，生产环境可以启用压缩。

## 🚀 部署到Chrome

1. 运行构建：`npm run build`
2. 打开Chrome扩展页面：`chrome://extensions/`
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择`dist`目录

构建验证通过后，所有文件都会正确部署到Chrome扩展中。
