import React, { useState } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Plus,
  Link,
  Monitor
} from 'lucide-react';
import { WorkSpace, Website } from '@/types/workspace';
import WebsiteList from './WebsiteList';
import DropdownMenu from './DropdownMenu';
import EditWorkspaceModal from './EditWorkspaceModal';
import AddWebsiteModal from './AddWebsiteModal';
import EditWebsiteModal from './EditWebsiteModal';
import ConfirmDialog from './ConfirmDialog';

interface WorkspaceItemProps {
  workspace: WorkSpace;
  isActive: boolean;
  isExpanded: boolean;
  onWorkspaceClick: () => void;
  onToggleExpand: () => void;
  onUpdateWorkspace: (updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: () => void;
  onAddCurrentTab: () => void;
  onAddWebsiteUrl: (url: string) => void;
  onRemoveWebsite: (websiteId: string) => void;
  onUpdateWebsite: (websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onReorderWebsites: (websiteIds: string[]) => void;
}

/**
 * 工作区项目组件
 */
const WorkspaceItem: React.FC<WorkspaceItemProps> = ({
  workspace,
  isActive,
  isExpanded,
  onWorkspaceClick,
  onToggleExpand,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onAddCurrentTab,
  onAddWebsiteUrl,
  onRemoveWebsite,
  onUpdateWebsite,
  onReorderWebsites,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditWebsiteModal, setShowEditWebsiteModal] = useState(false);
  const [editingWebsite, setEditingWebsite] = useState<Website | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  /**
   * 处理菜单项点击
   */
  const handleMenuClick = (action: string) => {
    setShowDropdown(false);
    
    switch (action) {
      case 'edit':
        setShowEditModal(true);
        break;
      case 'delete':
        setShowDeleteConfirm(true);
        break;
      case 'add-current':
        onAddCurrentTab();
        break;
      case 'add-url':
        setShowAddModal(true);
        break;
    }
  };

  /**
   * 处理编辑工作区
   */
  const handleEditWorkspace = (updates: { name?: string; icon?: string; color?: string }) => {
    onUpdateWorkspace(updates);
    setShowEditModal(false);
  };

  /**
   * 处理删除确认
   */
  const handleDeleteConfirm = () => {
    onDeleteWorkspace();
    setShowDeleteConfirm(false);
  };

  /**
   * 处理添加网站URL
   */
  const handleAddWebsite = (url: string) => {
    onAddWebsiteUrl(url);
    setShowAddModal(false);
  };

  /**
   * 处理编辑网站
   */
  const handleEditWebsite = (website: Website) => {
    setEditingWebsite(website);
    setShowEditWebsiteModal(true);
  };

  /**
   * 处理保存网站编辑
   */
  const handleSaveWebsiteEdit = (updates: { url?: string; title?: string; isPinned?: boolean }) => {
    if (editingWebsite) {
      onUpdateWebsite(editingWebsite.id, updates);
      setShowEditWebsiteModal(false);
      setEditingWebsite(null);
    }
  };

  const menuItems = [
    {
      id: 'add-current',
      label: '添加当前标签页',
      icon: Monitor,
    },
    {
      id: 'add-url',
      label: '添加网站URL',
      icon: Link,
    },
    {
      id: 'edit',
      label: '编辑工作区',
      icon: Edit,
    },
    {
      id: 'delete',
      label: '删除工作区',
      icon: Trash2,
      className: 'text-red-400 hover:text-red-300',
    },
  ];

  return (
    <div className={`workspace-item ${isActive ? 'active' : ''}`}>
      {/* 工作区头部 */}
      <div className="flex items-center justify-between">
        <div 
          className="flex items-center gap-3 flex-1 cursor-pointer"
          onClick={onWorkspaceClick}
        >
          {/* 展开/折叠按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand();
            }}
            className="p-1 hover:bg-slate-600 rounded transition-colors duration-150"
          >
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-slate-400" />
            ) : (
              <ChevronRight className="w-4 h-4 text-slate-400" />
            )}
          </button>

          {/* 工作区图标 */}
          <div 
            className="w-8 h-8 rounded-lg flex items-center justify-center text-lg"
            style={{ backgroundColor: workspace.color + '20', color: workspace.color }}
          >
            {workspace.icon}
          </div>

          {/* 工作区信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-white truncate">
                {workspace.name}
              </h3>
              {isActive && (
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              )}
            </div>
            <p className="text-xs text-slate-400">
              {workspace.websites.length} 个网站
            </p>
          </div>
        </div>

        {/* 更多操作按钮 */}
        <div className="relative">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowDropdown(!showDropdown);
            }}
            className="p-2 hover:bg-slate-600 rounded transition-colors duration-150"
          >
            <MoreVertical className="w-4 h-4 text-slate-400" />
          </button>

          {/* 下拉菜单 */}
          {showDropdown && (
            <DropdownMenu
              items={menuItems}
              onItemClick={handleMenuClick}
              onClose={() => setShowDropdown(false)}
            />
          )}
        </div>
      </div>

      {/* 网站列表 */}
      {isExpanded && workspace.websites.length > 0 && (
        <div className="mt-3 ml-7">
          <WebsiteList
            websites={workspace.websites}
            onRemoveWebsite={onRemoveWebsite}
            onEditWebsite={handleEditWebsite}
            onReorderWebsites={onReorderWebsites}
          />
        </div>
      )}

      {/* 空状态 */}
      {isExpanded && workspace.websites.length === 0 && (
        <div className="mt-3 ml-7 p-4 border-2 border-dashed border-slate-600 rounded-lg text-center">
          <Plus className="w-6 h-6 text-slate-500 mx-auto mb-2" />
          <p className="text-sm text-slate-400 mb-3">
            还没有添加任何网站
          </p>
          <div className="flex gap-2 justify-center">
            <button
              onClick={onAddCurrentTab}
              className="text-xs btn-ghost px-2 py-1"
            >
              <Monitor className="w-3 h-3" />
              当前标签页
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="text-xs btn-ghost px-2 py-1"
            >
              <Link className="w-3 h-3" />
              添加URL
            </button>
          </div>
        </div>
      )}

      {/* 编辑工作区模态框 */}
      {showEditModal && (
        <EditWorkspaceModal
          workspace={workspace}
          onClose={() => setShowEditModal(false)}
          onSave={handleEditWorkspace}
        />
      )}

      {/* 添加网站模态框 */}
      {showAddModal && (
        <AddWebsiteModal
          onClose={() => setShowAddModal(false)}
          onAdd={handleAddWebsite}
        />
      )}

      {/* 编辑网站模态框 */}
      {showEditWebsiteModal && editingWebsite && (
        <EditWebsiteModal
          website={editingWebsite}
          onClose={() => {
            setShowEditWebsiteModal(false);
            setEditingWebsite(null);
          }}
          onSave={handleSaveWebsiteEdit}
        />
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <ConfirmDialog
          title="删除工作区"
          message={`确定要删除工作区"${workspace.name}"吗？此操作无法撤销。`}
          confirmText="删除"
          confirmButtonClass="btn-danger"
          onConfirm={handleDeleteConfirm}
          onCancel={() => setShowDeleteConfirm(false)}
        />
      )}
    </div>
  );
};

export default WorkspaceItem;
